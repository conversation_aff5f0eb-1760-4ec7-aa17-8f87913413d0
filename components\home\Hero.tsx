'use client';

import Link from 'next/link';
import TypewriterEffect from './TypewriterEffect';

export default function Hero() {
  return (
    <section className="w-full bg-white md:px-8 lg:px-20 py-8 md:py-12 lg:py-16 h-screen overflow-hidden relative">
      {/* Background Video */}
      <video
        src="/video.mp4"
        autoPlay
        muted
        loop
        playsInline
        className="absolute inset-0 h-full w-full object-cover -z-20"
      />

      {/* Overlay */}
      <div className="absolute inset-0 bg-black/80 -z-10" />

      {/* Logo Section - Top positioned */}
      <div className="relative z-10 pt-[5%] md:pt-6 sm:pt-10 md:pl-[12%] pl-[2%]">
        <div className="flex items-center gap-3">
          <img
            src="/logo-icon.png"
            alt="Logo Icon"
            className="w-8 h-8 sm:w-10 sm:h-10 flex-shrink-0"
          />
          <div>
            <h1 className="text-white font-semibold text-lg sm:text-xl md:text-2xl leading-tight">
              Prolytech
            </h1>
            <p className="text-[#05A0E2] text-[5px] sm:text-[5.5px] md:text-[6px] font-bold tracking-wide uppercase">
              DEFINING THE CURVE OF WHAT'S NEXT
            </p>
          </div>
        </div>
      </div>

      {/* Main Content Container - Following reference layout structure */}
      <div className="relative z-10 h-full flex items-center">
        <div className="max-w-7xl mx-auto flex flex-col md:flex-row justify-between items-start gap-6 md:gap-8 text-white w-full">
          {/* Left Column - Title Section */}
          <div className="flex-1 max-w-md px-0 md:px-4 lg:px-8 xl:px-12">
            <p className="text-xs font-semibold text-[#05A0E2] uppercase tracking-wide mb-2">Vision & Mission</p>
            <h2 className="text-lg sm:text-4xl md:text-[24px] lg:text-[28px] font-normal text-white leading-[1.2]">
              We Engineer<br />
              <span className="whitespace-nowrap">Impactful Software—at Scale.</span>
            </h2>
          </div>

          {/* Right Column - Description and CTA */}
          <div className="flex-1 max-w-lg">
            <p className="text-sm md:text-sm text-gray-200 leading-relaxed mb-6">
              We help brands, communities, and platforms bring bold ideas to life. <br />
              From hyper-growth startups to enterprise transformations, our remote-first <br />
              teams deliver products that are intuitive, intelligent, and built to evolve.
            </p>

            {/* Main Heading with Typewriter Effect */}
            <h1 className="text-white text-2xl sm:text-3xl md:text-[32px] font-semibold leading-snug mb-4">
              <span className="block mb-2">Powering the Next Wave</span>
              <span className="flex flex-wrap items-center gap-2">
                <span>of</span>
                <TypewriterEffect
                  phrases={[
                    'Digital Innovation',
                    'Intelligent Automation',
                    'Scalable Growth',
                    'Cloud Scalability'
                  ]}
                  className="text-cyan-400"
                  typingSpeed={50}
                  deletingSpeed={25}
                  pauseDuration={2500}
                />
              </span>
            </h1>

            {/* CTA Button */}
            <Link
              href="/contact"
              className="mt-6 inline-block px-6 py-2 text-white font-medium text-sm md:text-base rounded-full shadow-md transition-all duration-300
              bg-[linear-gradient(to_bottom,#04E6FC,#0664CC)] hover:bg-[linear-gradient(to_bottom,#04c9de,#0559b4)]"
            >
              Schedule a Consultation
            </Link>
          </div>
        </div>
      </div>

      {/* Mobile Layout Override */}
      <div className="md:hidden absolute inset-0 z-10 flex flex-col h-full">
        {/* Video space at top - takes up remaining space */}
        <div className="flex-1"></div>

        {/* Content container at bottom */}
        <div className="px-[5%] pb-[25%] space-y-6">
          {/* Vision & Mission Label */}
          <p className="text-xs font-semibold text-[#05A0E2] uppercase tracking-wide mb-2">Vision & Mission</p>

          {/* Main Content Section */}
          <div>
            {/* Main Heading */}
            <h1 className="text-white text-2xl font-semibold leading-snug">
              <span className="block mb-2">Powering the Next Wave</span>
              <span className="flex flex-wrap items-center gap-2">
                <span>of</span>
                <TypewriterEffect
                  phrases={[
                    'Digital Innovation',
                    'Intelligent Automation',
                    'Scalable Growth',
                    'Cloud Scalability'
                  ]}
                  className="text-cyan-400"
                  typingSpeed={50}
                  deletingSpeed={25}
                  pauseDuration={2500}
                />
              </span>
            </h1>

            {/* Description */}
            <p className="text-gray-200 text-sm mt-4 leading-relaxed">
              We architect and deliver high-performance platforms—social, transactional, and intelligent—at startup speed and enterprise scale.
            </p>

            {/* CTA Button */}
            <Link
              href="/contact"
              className="mt-6 inline-block px-6 py-2 text-white font-medium text-sm rounded-full shadow-md transition-all duration-300
              bg-[linear-gradient(to_bottom,#04E6FC,#0664CC)] hover:bg-[linear-gradient(to_bottom,#04c9de,#0559b4)] w-full text-center"
            >
              Schedule a Consultation
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
}